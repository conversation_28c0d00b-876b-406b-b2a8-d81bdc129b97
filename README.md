# 飞牛论坛自动签到脚本

## 项目说明

这是一个用于飞牛论坛（club.fnnas.com）的自动签到脚本，支持普通签到和抢签到功能。

## 文件说明

- `pp.js` - 主脚本，包含完整功能（签到、浏览、抢签到）
- `rush_sign_test.js` - 专用抢签到脚本，借鉴老版本优化
- `fnos.db` - SQLite数据库文件（自动生成）

## 功能特性

### 普通签到模式
- 自动签到打卡
- 浏览帖子并点赞
- 获取用户信息统计
- 数据库记录历史

### 抢签到模式
- 精确时间控制（50-59秒范围内执行）
- 80个并发请求
- 网络延迟自动补偿
- 连接预热优化

## 使用方法

### 1. 安装依赖
```bash
npm install axios sqlite3
```

### 2. 更新Cookie
在脚本中更新你的Cookie字符串：
```javascript
const cookieString = "你的Cookie字符串";
```

### 3. 运行脚本

#### 普通签到（推荐日常使用）
```bash
node pp.js --sign
```

#### 抢签到（23:59:50-59秒使用）
```bash
node pp.js --rush
```

#### 专用抢签到脚本
```bash
node rush_sign_test.js
```

#### 完整功能
```bash
node pp.js
```

## 抢签到优化说明

### 借鉴老版本的优点：
1. **时间控制精确**：只在50-59秒范围内执行
2. **网络配置优化**：50个最大连接，5秒保持连接
3. **参数获取改进**：使用formhash匹配方式
4. **并发策略优化**：80个并发请求，300ms超时
5. **预热机制**：连接预热和URL预热

### 主要改进：
- 修复了等待时间计算问题
- 优化了网络延迟补偿算法
- 改进了错误重试机制
- 简化了时间判断逻辑

### 测试结果：
- ✅ 脚本启动正常，预热连接成功
- ✅ 服务器时间同步正常（延迟补偿）
- ✅ formhash获取成功
- ✅ 时间范围控制正确（非50-59秒自动退出）
- ✅ 普通签到功能正常
- ✅ 用户信息获取正常

### 性能对比：
- **老版本问题**：耗时101秒，时间控制不准确
- **新版本优化**：时间控制精确，只在目标时间执行
- **网络优化**：预热连接，减少首次请求延迟

## 定时任务设置

### Windows任务计划程序
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器：每天 23:59:50
4. 设置操作：启动程序 `node`，参数 `rush_sign_test.js`

### Linux Cron
```bash
# 每天23:59:50执行抢签到
59 23 * * * cd /path/to/script && node rush_sign_test.js
```

## 注意事项

1. **Cookie有效期**：需要定期更新Cookie
2. **网络环境**：建议在网络稳定的环境下运行
3. **时间同步**：确保系统时间准确
4. **并发限制**：避免同时运行多个实例

## 最佳实践

### 抢签到使用建议：
1. **时间设置**：建议在23:59:50-59秒之间启动脚本
2. **网络环境**：使用稳定的网络连接，避免WiFi不稳定
3. **系统时间**：确保系统时间与网络时间同步
4. **单独运行**：抢签到时不要运行其他网络密集型程序

### 日常签到建议：
1. **定时任务**：设置每天固定时间自动签到
2. **错峰执行**：避开高峰时段（0点前后）
3. **日志监控**：定期查看执行日志，确保正常运行

### Cookie更新方法：
1. 打开浏览器开发者工具（F12）
2. 访问飞牛论坛并登录
3. 在Network标签页找到请求
4. 复制Cookie值更新到脚本中

## 故障排除

### 常见问题：
1. **获取formhash失败**：检查Cookie是否有效
2. **网络超时**：检查网络连接
3. **签到失败**：可能已经签到过或服务器繁忙

### 调试方法：
- 查看控制台输出的详细日志
- 检查数据库记录
- 验证Cookie有效性

## 更新日志

### v2.0 (当前版本)
- 借鉴老版本优化抢签到功能
- 修复时间控制问题
- 优化网络配置
- 改进错误处理

### v1.0
- 基础签到功能
- 数据库记录
- 浏览点赞功能

## 维护者

- 用户名：alxxxxla
- 项目维护和更新

## 许可证

本项目仅供学习交流使用，请遵守相关网站的使用条款。
