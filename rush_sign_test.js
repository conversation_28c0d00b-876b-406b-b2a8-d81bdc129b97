const axios = require('axios');
const https = require('https');

// 抢签到专用脚本 - 借鉴老版本优化
class RushSignOptimized {
    constructor() {
        this.baseUrl = "https://club.fnnas.com";
        this.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Referer": "https://club.fnnas.com/plugin.php?id=zqlj_sign",
            "Connection": "keep-alive"
        };
        
        // 借鉴老版本的配置
        this.config = {
            concurrentRequests: 80,
            signRequestTimeout: 300,
            maxAdvanceTime: 100,
            advanceTimeOffset: 20,
            signAttemptDuration: 8000,
            batchRequestInterval: 30,
            formhashMaxRetries: 3,
            formhashRetryInterval: 100,
            signUrlWarmupCount: 3
        };
        
        // 借鉴老版本的网络配置
        this.client = axios.create({
            httpsAgent: new https.Agent({
                rejectUnauthorized: false,
                keepAlive: true,
                keepAliveMsecs: 5000,
                maxSockets: 50,
                maxFreeSockets: 50
            }),
            timeout: 8000
        });
    }

    login(cookieString) {
        if (!cookieString) {
            throw new Error("Cookie不能为空");
        }
        this.client.defaults.headers.Cookie = cookieString;
    }

    async rushSign() {
        console.log("=== 抢签到模式启动 ===");
        
        // 预热连接
        console.log("开始预热连接...");
        try {
            await this.client.head(this.baseUrl);
            console.log("预热连接成功");
        } catch (e) {
            console.log("预热连接失败，继续执行");
        }

        // 添加时间同步检查
        let serverTimeOffset = 0;
        let networkDelay = 0;

        try {
            const startTime = Date.now();
            const { headers } = await this.client.head(this.baseUrl);
            const endTime = Date.now();
            networkDelay = Math.floor((endTime - startTime) / 2);

            if (headers.date) {
                const serverTime = new Date(headers.date).getTime() + networkDelay;
                serverTimeOffset = serverTime - Date.now();
                console.log(`服务器时间偏移: ${serverTimeOffset}ms`);
                console.log(`网络延迟: ${networkDelay}ms`);
            }
        } catch (e) {
            console.log("获取服务器时间失败，使用本地时间");
        }

        // 获取formhash (借鉴老版本的重试逻辑)
        console.log("开始获取formhash...");
        let formhash;
        for (let i = 0; i < this.config.formhashMaxRetries; i++) {
            try {
                const { data: text } = await this.client.get(
                    `${this.baseUrl}/plugin.php?id=zqlj_sign`,
                    {
                        headers: this.headers,
                        timeout: 5000
                    }
                );
                const formhashMatch = text.match(/formhash=(\w+)['"]/)
                if (formhashMatch) {
                    formhash = formhashMatch[1];
                    console.log("获取到的 formhash:", formhash);
                    break;
                }
            } catch (e) {
                console.log(`第${i + 1}次获取formhash失败，${i < this.config.formhashMaxRetries - 1 ? '正在重试...' : '放弃重试'}`);
                if (i === this.config.formhashMaxRetries - 1) {
                    throw new Error("获取 formhash 失败");
                }
                await new Promise(resolve => setTimeout(resolve, this.config.formhashRetryInterval));
            }
        }

        const signUrl = `${this.baseUrl}/plugin.php?id=zqlj_sign&sign=${formhash}`;
        let attemptCount = 0;
        let success = false;
        let result = null;

        // 提前准备好请求对象并预热连接
        const requests = Array(this.config.concurrentRequests).fill().map(() => ({
            url: signUrl,
            headers: this.headers,
            timeout: this.config.signRequestTimeout
        }));

        // 预热签到URL的连接
        try {
            const warmupPromises = Array(this.config.signUrlWarmupCount).fill().map(() => this.client.head(signUrl));
            await Promise.all(warmupPromises);
            console.log("签到URL预热成功");
        } catch (e) {
            console.log("预热签到连接失败，继续执行");
        }

        // 根据网络延迟动态调整提前时间
        const advanceTime = Math.min(networkDelay + this.config.advanceTimeOffset, this.config.maxAdvanceTime);
        console.log(`根据网络延迟(${networkDelay}ms)，设置提前${advanceTime}ms开始请求`);

        // 等待接近0点 - 借鉴老版本的时间控制逻辑
        const now = new Date();
        const targetTime = new Date(now);
        targetTime.setHours(24, 0, 0, 0);

        // 计算当前是否接近目标时间
        const currentSeconds = now.getSeconds();
        const isNearTarget = currentSeconds >= 50 && currentSeconds <= 59;

        if (!isNearTarget) {
            console.log("当前不在目标时间范围内，脚本将退出");
            return {
                success: false,
                message: "不在目标执行时间范围内",
                time: new Date().toLocaleString(),
                attempts: 0
            };
        }

        // 计算等待时间
        const waitTime = targetTime.getTime() - now.getTime() - advanceTime;
        if (waitTime > 0) {
            console.log(`等待${waitTime}ms后开始抢签到...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }

        // 创建一个控制器用于取消请求
        const controller = new AbortController();

        // 立即并发发送所有请求 - 借鉴老版本的策略
        const startTime = Date.now();
        const endTime = startTime + this.config.signAttemptDuration;

        console.log(`开始抢签到，使用${this.config.concurrentRequests}个并发请求...`);

        while (!success && Date.now() < endTime) {
            // 同时发送所有请求
            const batchRequests = requests.map(async (req) => {
                attemptCount++;
                try {
                    const { data } = await this.client.get(req.url, {
                        headers: req.headers,
                        timeout: req.timeout,
                        signal: controller.signal
                    });

                    if (data.includes("恭喜您，打卡成功")) {
                        const rewardMatch = data.match(/获得(\d+)飞牛币/);
                        return {
                            success: true,
                            message: rewardMatch ? `抢签到成功，获得${rewardMatch[1]}飞牛币` : "抢签到成功"
                        };
                    }
                    return null;
                } catch (e) {
                    return null;
                }
            });

            // 等待任意一个请求成功
            const results = await Promise.race([
                Promise.all(batchRequests),
                new Promise(resolve => setTimeout(resolve, this.config.batchRequestInterval))
            ]);

            if (Array.isArray(results)) {
                const successResult = results.find(r => r?.success);
                if (successResult) {
                    success = true;
                    result = successResult;
                    controller.abort();
                    break;
                }
            }
        }

        return {
            success: result?.success || false,
            message: result?.message || "抢签到失败",
            time: new Date().toLocaleString(),
            attempts: attemptCount
        };
    }
}

async function main() {
    const fnos = new RushSignOptimized();
    const cookieString = "Hm_lvt_f44c77c038dd3fcfea6a8bcb439067e5=**********,**********,**********; HMACCOUNT=70BB84CE4124CFAE; pvRK_2132_saltkey=xzHII6ON; pvRK_2132_lastvisit=**********; pvRK_2132_sid=BmqWZ4; pvRK_2132_sendmail=1; pvRK_2132_seccodecSBmqWZ4=2.c479789a5894fc898c; pvRK_2132_ulastactivity=ae50ExJXjgPt5fVr8TDBASSAa%2B3KBTWsGtyPw75SJ5Eh%2FuoPs6NQ; pvRK_2132_auth=f6d3yxHiAlWgFgKmy8bHywte31%2FMikPmmZ8tVIQADPiPQjD3RMXnLQWjXbl8i9z%2BetcjCoELNwBSp4KRCn8jZInT5w; pvRK_2132_lastcheckfeed=37561%7C1754173942; pvRK_2132_checkfollow=1; pvRK_2132_lip=**************%2C1754173937; pvRK_2132_myrepeat_rr=R0; pvRK_2132_checkpm=1; pvRK_2132_lastact=**********%09misc.php%09patch; Hm_lpvt_f44c77c038dd3fcfea6a8bcb439067e5=**********";

    try {
        fnos.login(cookieString);
        const result = await fnos.rushSign();
        console.log(`状态: ${result.message}`);
        console.log(`总共尝试次数：${result.attempts}次`);
    } catch (e) {
        console.log(`运行异常: ${e.message}`);
    }
}

main();
